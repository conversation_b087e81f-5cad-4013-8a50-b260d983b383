<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bulk Episode Links Demo - Deshiflix Admin</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
            background: #f1f1f1;
            margin: 0;
            padding: 20px;
        }
        
        .admin-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border: 1px solid #ccd0d4;
            box-shadow: 0 1px 1px rgba(0,0,0,.04);
        }
        
        .admin-header {
            background: #23282d;
            color: white;
            padding: 15px 20px;
            border-bottom: 1px solid #ccd0d4;
        }
        
        .admin-header h1 {
            margin: 0;
            font-size: 23px;
            font-weight: 400;
        }
        
        .metabox {
            margin: 20px;
            background: #fff;
            border: 1px solid #e5e5e5;
            box-shadow: 0 1px 1px rgba(0,0,0,.04);
        }
        
        .metabox-title {
            background: #f1f1f1;
            border-bottom: 1px solid #e5e5e5;
            padding: 12px 15px;
            font-size: 14px;
            font-weight: 600;
            margin: 0;
        }
        
        .metabox-content {
            padding: 20px;
        }
        
        .form-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .form-table td {
            padding: 15px 10px;
            border-bottom: 1px solid #f0f0f0;
            vertical-align: top;
        }
        
        .form-table td.label {
            width: 200px;
            font-weight: 600;
            background: #f9f9f9;
        }
        
        .form-table input[type="number"],
        .form-table input[type="text"],
        .form-table input[type="url"],
        .form-table select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            width: 200px;
        }
        
        .button {
            background: #0073aa;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 3px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            font-size: 13px;
        }
        
        .button:hover {
            background: #005a87;
        }
        
        .button-primary {
            background: #00a32a;
        }
        
        .button-primary:hover {
            background: #008a20;
        }
        
        .episode-field {
            border: 1px solid #ddd;
            padding: 15px;
            margin: 10px 0;
            background: #f9f9f9;
            border-radius: 5px;
        }
        
        .episode-field h4 {
            margin: 0 0 10px 0;
            color: #333;
            font-size: 16px;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
        }
        
        .episode-field table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .episode-field table td {
            padding: 8px;
            border: none;
        }
        
        .episode-field table td:first-child {
            width: 150px;
            font-weight: 600;
            color: #555;
        }
        
        .episode-field input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 3px;
        }
        
        .success-message {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
            padding: 10px;
            border-radius: 3px;
            margin: 10px 0;
        }
        
        .info-box {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        
        .info-box h3 {
            margin: 0 0 10px 0;
            color: #0073aa;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
        }
        
        .feature-list li {
            padding: 5px 0;
            border-bottom: 1px solid #eee;
        }
        
        .feature-list li:before {
            content: "✓ ";
            color: #00a32a;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <div class="admin-header">
            <h1>Deshiflix Admin - TV Show: "Money Heist" Edit</h1>
        </div>
        
        <div class="metabox">
            <h3 class="metabox-title">Bulk Episode Links</h3>
            <div class="metabox-content">
                <div class="info-box">
                    <h3>🎬 নতুন ফিচার: বাল্ক এপিসোড লিংক যুক্ত করুন</h3>
                    <p>এখন আপনি একসাথে একাধিক এপিসোডের ডাউনলোড লিংক যুক্ত করতে পারবেন। প্রতিটি এপিসোডের জন্য আলাদা আলাদা নাম্বারিং এবং লিংক সেট করা যাবে।</p>
                    
                    <ul class="feature-list">
                        <li>একসাথে ১০০টি পর্যন্ত এপিসোড যুক্ত করুন</li>
                        <li>অটোমেটিক এপিসোড নাম্বারিং</li>
                        <li>কমন কোয়ালিটি, ভাষা এবং টাইপ সেট করুন</li>
                        <li>প্রতিটি এপিসোডের জন্য আলাদা সাইজ এবং লিংক</li>
                        <li>সিজন নাম্বার কাস্টমাইজেশন</li>
                    </ul>
                </div>
                
                <table class="form-table">
                    <tr>
                        <td class="label">Number of Episodes</td>
                        <td>
                            <input type="number" value="5" min="1" max="100" style="width: 80px;">
                            <button class="button">Generate Fields</button>
                        </td>
                    </tr>
                    <tr>
                        <td class="label">Season Number</td>
                        <td>
                            <input type="number" value="1" min="1" style="width: 80px;">
                        </td>
                    </tr>
                    <tr>
                        <td class="label">Starting Episode Number</td>
                        <td>
                            <input type="number" value="1" min="1" style="width: 80px;">
                        </td>
                    </tr>
                    <tr>
                        <td class="label">Link Type</td>
                        <td>
                            <select>
                                <option>Download</option>
                                <option>Watch online</option>
                                <option>Torrent</option>
                            </select>
                        </td>
                    </tr>
                    <tr>
                        <td class="label">Quality</td>
                        <td>
                            <select>
                                <option>720p</option>
                                <option>1080p</option>
                                <option>480p</option>
                                <option>4K</option>
                            </select>
                        </td>
                    </tr>
                    <tr>
                        <td class="label">Language</td>
                        <td>
                            <select>
                                <option>Bengali</option>
                                <option>Hindi</option>
                                <option>English</option>
                                <option>Tamil</option>
                            </select>
                        </td>
                    </tr>
                </table>
                
                <div style="margin-top: 20px;">
                    <h3 style="color: #0073aa; margin-bottom: 15px;">Episode Links</h3>
                    
                    <div class="episode-field">
                        <h4>Episode 1</h4>
                        <table>
                            <tr>
                                <td>Download URL:</td>
                                <td><input type="url" placeholder="Enter download link" value="https://example.com/money-heist-s01e01.mkv"></td>
                            </tr>
                            <tr>
                                <td>File Size:</td>
                                <td><input type="text" placeholder="e.g., 350MB, 1.2GB" value="450MB"></td>
                            </tr>
                        </table>
                    </div>
                    
                    <div class="episode-field">
                        <h4>Episode 2</h4>
                        <table>
                            <tr>
                                <td>Download URL:</td>
                                <td><input type="url" placeholder="Enter download link" value="https://example.com/money-heist-s01e02.mkv"></td>
                            </tr>
                            <tr>
                                <td>File Size:</td>
                                <td><input type="text" placeholder="e.g., 350MB, 1.2GB" value="420MB"></td>
                            </tr>
                        </table>
                    </div>
                    
                    <div class="episode-field">
                        <h4>Episode 3</h4>
                        <table>
                            <tr>
                                <td>Download URL:</td>
                                <td><input type="url" placeholder="Enter download link" value="https://example.com/money-heist-s01e03.mkv"></td>
                            </tr>
                            <tr>
                                <td>File Size:</td>
                                <td><input type="text" placeholder="e.g., 350MB, 1.2GB" value="380MB"></td>
                            </tr>
                        </table>
                    </div>
                    
                    <div class="episode-field">
                        <h4>Episode 4</h4>
                        <table>
                            <tr>
                                <td>Download URL:</td>
                                <td><input type="url" placeholder="Enter download link" value="https://example.com/money-heist-s01e04.mkv"></td>
                            </tr>
                            <tr>
                                <td>File Size:</td>
                                <td><input type="text" placeholder="e.g., 350MB, 1.2GB" value="465MB"></td>
                            </tr>
                        </table>
                    </div>
                    
                    <div class="episode-field">
                        <h4>Episode 5</h4>
                        <table>
                            <tr>
                                <td>Download URL:</td>
                                <td><input type="url" placeholder="Enter download link" value="https://example.com/money-heist-s01e05.mkv"></td>
                            </tr>
                            <tr>
                                <td>File Size:</td>
                                <td><input type="text" placeholder="e.g., 350MB, 1.2GB" value="440MB"></td>
                            </tr>
                        </table>
                    </div>
                    
                    <button class="button button-primary" style="margin-top: 15px; padding: 12px 25px; font-size: 16px;">Save All Episode Links</button>
                    
                    <div class="success-message" style="margin-top: 15px;">
                        ✅ Episodes saved successfully! 5 episodes have been created with download links.
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
