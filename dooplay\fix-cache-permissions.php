<?php
/**
 * Cache Permission Fix Script for Dooplay Theme
 * Run this script once to fix cache directory permissions
 */

// Define paths
$cache_dir = __DIR__ . '/inc/core/dbmvs/cache/';
$theme_dir = __DIR__;

echo "<h2>🔧 Dooplay Cache Permission Fix</h2>\n";
echo "<p>Fixing cache directory permissions...</p>\n";

// Check if cache directory exists
if (!is_dir($cache_dir)) {
    echo "<p style='color: red;'>❌ Cache directory does not exist: $cache_dir</p>\n";
    echo "<p>Creating cache directory...</p>\n";
    
    if (mkdir($cache_dir, 0755, true)) {
        echo "<p style='color: green;'>✅ Cache directory created successfully</p>\n";
    } else {
        echo "<p style='color: red;'>❌ Failed to create cache directory</p>\n";
        exit;
    }
}

// Check current permissions
$current_perms = fileperms($cache_dir);
$current_perms_octal = substr(sprintf('%o', $current_perms), -4);

echo "<p>📁 Cache directory: <code>$cache_dir</code></p>\n";
echo "<p>🔒 Current permissions: <code>$current_perms_octal</code></p>\n";

// Check if directory is writable
if (is_writable($cache_dir)) {
    echo "<p style='color: green;'>✅ Cache directory is writable</p>\n";
} else {
    echo "<p style='color: orange;'>⚠️ Cache directory is not writable, attempting to fix...</p>\n";
    
    // Try to fix permissions
    if (chmod($cache_dir, 0755)) {
        echo "<p style='color: green;'>✅ Permissions fixed successfully</p>\n";
    } else {
        echo "<p style='color: red;'>❌ Failed to fix permissions automatically</p>\n";
        echo "<p><strong>Manual Fix Required:</strong></p>\n";
        echo "<ol>\n";
        echo "<li>Right-click on the cache folder in Windows Explorer</li>\n";
        echo "<li>Select 'Properties' → 'Security' tab</li>\n";
        echo "<li>Click 'Edit' and give 'Full Control' to 'Everyone' or your web server user</li>\n";
        echo "<li>Apply changes and try again</li>\n";
        echo "</ol>\n";
    }
}

// Clean up old cache files that might be causing issues
echo "<p>🧹 Cleaning up problematic cache files...</p>\n";

$cleaned_files = 0;
$cache_files = glob($cache_dir . '*');

foreach ($cache_files as $file) {
    $filename = basename($file);
    
    // Remove files with empty extensions or problematic names
    if (preg_match('/\.$/', $filename) || preg_match('/^(seasons|episodes)\.?$/', $filename)) {
        if (unlink($file)) {
            echo "<p style='color: green;'>🗑️ Removed problematic file: $filename</p>\n";
            $cleaned_files++;
        } else {
            echo "<p style='color: red;'>❌ Failed to remove: $filename</p>\n";
        }
    }
}

if ($cleaned_files > 0) {
    echo "<p style='color: green;'>✅ Cleaned up $cleaned_files problematic cache files</p>\n";
} else {
    echo "<p>ℹ️ No problematic cache files found</p>\n";
}

// Test cache write functionality
echo "<p>🧪 Testing cache write functionality...</p>\n";

$test_file = $cache_dir . 'test_write_' . time() . '.tmp';
$test_data = 'Cache write test - ' . date('Y-m-d H:i:s');

if (file_put_contents($test_file, $test_data)) {
    echo "<p style='color: green;'>✅ Cache write test successful</p>\n";
    
    // Clean up test file
    if (unlink($test_file)) {
        echo "<p style='color: green;'>✅ Test file cleaned up</p>\n";
    }
} else {
    echo "<p style='color: red;'>❌ Cache write test failed</p>\n";
    echo "<p><strong>Possible solutions:</strong></p>\n";
    echo "<ul>\n";
    echo "<li>Check if XAMPP is running as Administrator</li>\n";
    echo "<li>Disable Windows UAC temporarily</li>\n";
    echo "<li>Move your XAMPP installation to a different drive (not C:)</li>\n";
    echo "<li>Add cache directory to Windows Defender exclusions</li>\n";
    echo "</ul>\n";
}

// Check PHP configuration
echo "<p>⚙️ PHP Configuration Check:</p>\n";
echo "<ul>\n";
echo "<li>PHP Version: " . PHP_VERSION . "</li>\n";
echo "<li>Upload Max Filesize: " . ini_get('upload_max_filesize') . "</li>\n";
echo "<li>Post Max Size: " . ini_get('post_max_size') . "</li>\n";
echo "<li>Memory Limit: " . ini_get('memory_limit') . "</li>\n";
echo "<li>Max Execution Time: " . ini_get('max_execution_time') . "s</li>\n";
echo "</ul>\n";

// WordPress specific checks
if (defined('ABSPATH')) {
    echo "<p>🌐 WordPress Environment Detected</p>\n";
    echo "<ul>\n";
    echo "<li>WordPress Version: " . get_bloginfo('version') . "</li>\n";
    echo "<li>Active Theme: " . get_template() . "</li>\n";
    echo "<li>WP Debug: " . (WP_DEBUG ? 'Enabled' : 'Disabled') . "</li>\n";
    echo "</ul>\n";
} else {
    echo "<p>ℹ️ Run this script from WordPress admin or include wp-config.php for more detailed checks</p>\n";
}

// Recommendations
echo "<h3>📋 Recommendations</h3>\n";
echo "<ol>\n";
echo "<li><strong>Backup your site</strong> before making any changes</li>\n";
echo "<li><strong>Run XAMPP as Administrator</strong> to avoid permission issues</li>\n";
echo "<li><strong>Use a dedicated development folder</strong> outside of C:\\ drive</li>\n";
echo "<li><strong>Disable antivirus real-time scanning</strong> for your development folder</li>\n";
echo "<li><strong>Clear browser cache</strong> after making changes</li>\n";
echo "</ol>\n";

// Alternative cache solution
echo "<h3>🔄 Alternative Cache Solution</h3>\n";
echo "<p>If file-based caching continues to cause issues, consider:</p>\n";
echo "<ul>\n";
echo "<li>Using WordPress transients instead of file cache</li>\n";
echo "<li>Disabling cache temporarily for development</li>\n";
echo "<li>Using a different cache directory with proper permissions</li>\n";
echo "</ul>\n";

echo "<hr>\n";
echo "<p style='color: green;'><strong>✅ Cache permission fix script completed!</strong></p>\n";
echo "<p>If you're still experiencing issues, please check the manual solutions above.</p>\n";

// Create a .htaccess file for cache directory security
$htaccess_content = "# Dooplay Cache Directory Protection\n";
$htaccess_content .= "Options -Indexes\n";
$htaccess_content .= "<Files \"*.php\">\n";
$htaccess_content .= "    Order allow,deny\n";
$htaccess_content .= "    Deny from all\n";
$htaccess_content .= "</Files>\n";

$htaccess_file = $cache_dir . '.htaccess';
if (!file_exists($htaccess_file)) {
    if (file_put_contents($htaccess_file, $htaccess_content)) {
        echo "<p style='color: green;'>🔒 Created .htaccess for cache directory security</p>\n";
    }
}

?>

<style>
body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
    max-width: 800px;
    margin: 20px auto;
    padding: 20px;
    background: #f1f1f1;
}

h2, h3 {
    color: #333;
    border-bottom: 2px solid #0073aa;
    padding-bottom: 10px;
}

p, li {
    line-height: 1.6;
}

code {
    background: #f0f0f0;
    padding: 2px 6px;
    border-radius: 3px;
    font-family: monospace;
}

ul, ol {
    margin-left: 20px;
}

hr {
    border: none;
    border-top: 1px solid #ccc;
    margin: 30px 0;
}
</style>
