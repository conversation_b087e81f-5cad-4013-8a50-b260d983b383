/* Bulk Episode Links Admin Styles */

#bulk-episode-links-container {
    background: #fff;
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 5px;
}

#bulk-episode-links-container .options-table-responsive {
    width: 100%;
    border-collapse: collapse;
}

#bulk-episode-links-container .options-table-responsive td {
    padding: 10px;
    border-bottom: 1px solid #eee;
    vertical-align: top;
}

#bulk-episode-links-container .options-table-responsive td.label {
    width: 200px;
    font-weight: bold;
    background: #f9f9f9;
}

#bulk-episode-links-container .options-table-responsive td.field {
    background: #fff;
}

#bulk-episode-links-container input[type="number"],
#bulk-episode-links-container input[type="text"],
#bulk-episode-links-container input[type="url"],
#bulk-episode-links-container select {
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 3px;
    font-size: 14px;
}

#bulk-episode-links-container .regular-text {
    width: 100%;
    max-width: 400px;
}

.episode-field {
    border: 1px solid #ddd !important;
    padding: 15px !important;
    margin-bottom: 10px !important;
    background: #f9f9f9 !important;
    border-radius: 5px;
}

.episode-field h4 {
    margin: 0 0 10px 0;
    color: #333;
    font-size: 16px;
    font-weight: bold;
    border-bottom: 1px solid #ddd;
    padding-bottom: 5px;
}

.episode-field table {
    width: 100% !important;
    border-collapse: collapse;
}

.episode-field table td {
    padding: 8px !important;
    border: none !important;
    vertical-align: middle;
}

.episode-field table td:first-child {
    width: 150px !important;
    font-weight: bold;
    color: #555;
}

.episode-field input[type="url"],
.episode-field input[type="text"] {
    width: 100% !important;
    padding: 8px;
    border: 1px solid #ccc;
    border-radius: 3px;
    font-size: 14px;
}

.episode-field input[type="url"]:focus,
.episode-field input[type="text"]:focus {
    border-color: #0073aa;
    box-shadow: 0 0 0 1px #0073aa;
    outline: none;
}

#episode_fields_container h3 {
    margin: 20px 0 15px 0;
    padding: 10px;
    background: #0073aa;
    color: white;
    border-radius: 3px;
    font-size: 18px;
    text-align: center;
}

#generate_episode_fields {
    background: #0073aa;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 3px;
    cursor: pointer;
    font-size: 14px;
}

#generate_episode_fields:hover {
    background: #005a87;
}

#save_bulk_episodes {
    background: #00a32a;
    color: white;
    border: none;
    padding: 12px 25px;
    border-radius: 3px;
    cursor: pointer;
    font-size: 16px;
    font-weight: bold;
}

#save_bulk_episodes:hover {
    background: #008a20;
}

#save_bulk_episodes:disabled {
    background: #ccc;
    cursor: not-allowed;
}

#bulk_save_status {
    margin-top: 10px;
    padding: 10px;
    border-radius: 3px;
    font-weight: bold;
}

#bulk_save_status div {
    padding: 8px;
    border-radius: 3px;
}

/* Success message */
#bulk_save_status .success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

/* Error message */
#bulk_save_status .error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* Info message */
#bulk_save_status .info {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

/* Responsive design */
@media (max-width: 768px) {
    #bulk-episode-links-container .options-table-responsive td.label {
        width: 100%;
        display: block;
        border-bottom: none;
        padding-bottom: 5px;
    }
    
    #bulk-episode-links-container .options-table-responsive td.field {
        width: 100%;
        display: block;
        padding-top: 5px;
    }
    
    .episode-field table td:first-child {
        width: 100% !important;
        display: block;
        padding-bottom: 5px !important;
    }
    
    .episode-field table td:last-child {
        width: 100% !important;
        display: block;
        padding-top: 5px !important;
    }
}

/* Loading animation */
.bulk-episode-loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #0073aa;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Form validation styles */
.episode-field input:invalid {
    border-color: #dc3545;
    box-shadow: 0 0 0 1px #dc3545;
}

.episode-field input:valid {
    border-color: #28a745;
}

/* Tooltip styles */
.bulk-episode-tooltip {
    position: relative;
    display: inline-block;
    cursor: help;
}

.bulk-episode-tooltip .tooltiptext {
    visibility: hidden;
    width: 200px;
    background-color: #333;
    color: #fff;
    text-align: center;
    border-radius: 6px;
    padding: 5px;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    margin-left: -100px;
    opacity: 0;
    transition: opacity 0.3s;
    font-size: 12px;
}

.bulk-episode-tooltip:hover .tooltiptext {
    visibility: visible;
    opacity: 1;
}

/* Progress bar for bulk operations */
.bulk-progress-container {
    width: 100%;
    background-color: #f0f0f0;
    border-radius: 5px;
    margin: 10px 0;
}

.bulk-progress-bar {
    width: 0%;
    height: 20px;
    background-color: #0073aa;
    border-radius: 5px;
    text-align: center;
    line-height: 20px;
    color: white;
    font-size: 12px;
    transition: width 0.3s ease;
}
