<?php
/*
* ----------------------------------------------------
* @author: Doothemes
* <AUTHOR> https://doothemes.com/
* @copyright: (c) 2021 Doothemes. All rights reserved
* ----------------------------------------------------
* @since 2.5.0
* Improved Cache Handler with Permission Error Handling
*/

class DDbmoviesCacheHandler {

    private static $cache_dir;
    private static $cache_enabled = true;
    private static $fallback_to_transients = false;

    /**
     * Initialize cache handler
     */
    public static function init() {
        self::$cache_dir = defined('DBMOVIES_CACHE_DIR') ? DBMOVIES_CACHE_DIR : get_template_directory() . '/inc/core/dbmvs/cache/';
        
        // Check if cache directory is writable
        if (!is_dir(self::$cache_dir)) {
            wp_mkdir_p(self::$cache_dir);
        }
        
        if (!is_writable(self::$cache_dir)) {
            self::$fallback_to_transients = true;
            error_log('Dooplay Cache: Directory not writable, falling back to transients');
        }
    }

    /**
     * Get cached data
     */
    public static function get($key, $group = 'default') {
        if (!self::$cache_enabled) {
            return false;
        }

        // Sanitize key
        $key = self::sanitize_key($key);
        
        if (self::$fallback_to_transients) {
            return get_transient("dbmovies_{$group}_{$key}");
        }

        $cache_file = self::$cache_dir . "{$group}_{$key}";
        
        if (!file_exists($cache_file)) {
            return false;
        }

        // Check if cache is expired
        $cache_time = defined('DBMOVIES_CACHE_TIM') ? DBMOVIES_CACHE_TIM : 172800; // 48 hours default
        if (filemtime($cache_file) + $cache_time < time()) {
            self::delete($key, $group);
            return false;
        }

        $data = file_get_contents($cache_file);
        return maybe_unserialize($data);
    }

    /**
     * Set cached data
     */
    public static function set($key, $data, $group = 'default', $expiration = null) {
        if (!self::$cache_enabled) {
            return false;
        }

        // Sanitize key
        $key = self::sanitize_key($key);
        
        if (self::$fallback_to_transients) {
            $expiration = $expiration ?: (defined('DBMOVIES_CACHE_TIM') ? DBMOVIES_CACHE_TIM : 172800);
            return set_transient("dbmovies_{$group}_{$key}", $data, $expiration);
        }

        $cache_file = self::$cache_dir . "{$group}_{$key}";
        
        try {
            $serialized_data = serialize($data);
            $result = file_put_contents($cache_file, $serialized_data, LOCK_EX);
            
            if ($result === false) {
                // If file write fails, fallback to transients
                self::$fallback_to_transients = true;
                $expiration = $expiration ?: (defined('DBMOVIES_CACHE_TIM') ? DBMOVIES_CACHE_TIM : 172800);
                return set_transient("dbmovies_{$group}_{$key}", $data, $expiration);
            }
            
            return true;
        } catch (Exception $e) {
            error_log('Dooplay Cache Error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Delete cached data
     */
    public static function delete($key, $group = 'default') {
        // Sanitize key
        $key = self::sanitize_key($key);
        
        if (self::$fallback_to_transients) {
            return delete_transient("dbmovies_{$group}_{$key}");
        }

        $cache_file = self::$cache_dir . "{$group}_{$key}";
        
        if (file_exists($cache_file)) {
            return unlink($cache_file);
        }
        
        return true;
    }

    /**
     * Clear all cache
     */
    public static function flush($group = null) {
        if (self::$fallback_to_transients) {
            // Clear transients (this is more complex, so we'll just disable cache temporarily)
            self::$cache_enabled = false;
            return true;
        }

        if (!is_dir(self::$cache_dir)) {
            return true;
        }

        $pattern = $group ? "{$group}_*" : "*";
        $files = glob(self::$cache_dir . $pattern);
        
        foreach ($files as $file) {
            if (is_file($file)) {
                unlink($file);
            }
        }
        
        return true;
    }

    /**
     * Sanitize cache key
     */
    private static function sanitize_key($key) {
        // Remove any characters that might cause file system issues
        $key = preg_replace('/[^a-zA-Z0-9_\-]/', '_', $key);
        // Ensure key is not empty
        if (empty($key)) {
            $key = 'default_' . md5(time());
        }
        return $key;
    }

    /**
     * Get cache statistics
     */
    public static function get_stats() {
        $stats = array(
            'cache_enabled' => self::$cache_enabled,
            'fallback_to_transients' => self::$fallback_to_transients,
            'cache_dir' => self::$cache_dir,
            'cache_dir_writable' => is_writable(self::$cache_dir),
            'cache_files_count' => 0,
            'cache_size' => 0
        );

        if (is_dir(self::$cache_dir)) {
            $files = glob(self::$cache_dir . "*");
            $stats['cache_files_count'] = count($files);
            
            foreach ($files as $file) {
                if (is_file($file)) {
                    $stats['cache_size'] += filesize($file);
                }
            }
        }

        return $stats;
    }

    /**
     * Enable/disable cache
     */
    public static function enable($enable = true) {
        self::$cache_enabled = $enable;
    }

    /**
     * Check if cache is working
     */
    public static function test() {
        $test_key = 'cache_test_' . time();
        $test_data = array('test' => true, 'timestamp' => time());
        
        // Test set
        $set_result = self::set($test_key, $test_data, 'test');
        if (!$set_result) {
            return array('success' => false, 'message' => 'Failed to set cache');
        }
        
        // Test get
        $get_result = self::get($test_key, 'test');
        if ($get_result !== $test_data) {
            return array('success' => false, 'message' => 'Failed to get cache or data mismatch');
        }
        
        // Test delete
        $delete_result = self::delete($test_key, 'test');
        if (!$delete_result) {
            return array('success' => false, 'message' => 'Failed to delete cache');
        }
        
        // Verify deletion
        $verify_result = self::get($test_key, 'test');
        if ($verify_result !== false) {
            return array('success' => false, 'message' => 'Cache not properly deleted');
        }
        
        return array('success' => true, 'message' => 'Cache is working properly');
    }

    /**
     * Get or set cache with callback
     */
    public static function remember($key, $callback, $group = 'default', $expiration = null) {
        $data = self::get($key, $group);
        
        if ($data !== false) {
            return $data;
        }
        
        // Data not in cache, execute callback
        if (is_callable($callback)) {
            $data = call_user_func($callback);
            self::set($key, $data, $group, $expiration);
            return $data;
        }
        
        return false;
    }
}

// Initialize cache handler
DDbmoviesCacheHandler::init();
