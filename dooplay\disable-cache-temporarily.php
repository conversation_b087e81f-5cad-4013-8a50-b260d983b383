<?php
/**
 * Temporary Cache Disable Script for Dooplay Theme
 * This script will disable file-based caching to avoid permission issues
 * Run this script once to disable caching temporarily
 */

// WordPress configuration
if (!defined('ABSPATH')) {
    // Try to find wp-config.php
    $wp_config_path = '';
    $current_dir = __DIR__;
    
    // Look for wp-config.php in parent directories
    for ($i = 0; $i < 10; $i++) {
        if (file_exists($current_dir . '/wp-config.php')) {
            $wp_config_path = $current_dir . '/wp-config.php';
            break;
        }
        $current_dir = dirname($current_dir);
    }
    
    if ($wp_config_path) {
        require_once $wp_config_path;
    }
}

echo "<h2>🔧 Dooplay Cache Disable Script</h2>\n";
echo "<p>This script will temporarily disable file-based caching to avoid permission issues.</p>\n";

// Create a cache disable flag file
$cache_disable_file = __DIR__ . '/inc/core/dbmvs/cache/.cache_disabled';
$cache_dir = __DIR__ . '/inc/core/dbmvs/cache/';

// Ensure cache directory exists
if (!is_dir($cache_dir)) {
    if (mkdir($cache_dir, 0755, true)) {
        echo "<p style='color: green;'>✅ Cache directory created</p>\n";
    } else {
        echo "<p style='color: red;'>❌ Failed to create cache directory</p>\n";
    }
}

// Create cache disable flag
if (file_put_contents($cache_disable_file, "Cache disabled on " . date('Y-m-d H:i:s'))) {
    echo "<p style='color: green;'>✅ Cache disabled successfully</p>\n";
} else {
    echo "<p style='color: orange;'>⚠️ Could not create cache disable flag, trying alternative method...</p>\n";
    
    // Alternative: Create a PHP constant file
    $disable_cache_php = __DIR__ . '/inc/core/dbmvs/disable_cache.php';
    $php_content = "<?php\n// Cache disabled flag\ndefine('DOOPLAY_CACHE_DISABLED', true);\n";
    
    if (file_put_contents($disable_cache_php, $php_content)) {
        echo "<p style='color: green;'>✅ Cache disabled via PHP constant</p>\n";
    } else {
        echo "<p style='color: red;'>❌ Failed to disable cache</p>\n";
    }
}

// Clean up existing problematic cache files
echo "<p>🧹 Cleaning up existing cache files...</p>\n";

$cleaned = 0;
if (is_dir($cache_dir)) {
    $files = glob($cache_dir . '*');
    foreach ($files as $file) {
        if (is_file($file) && basename($file) !== '.cache_disabled') {
            if (unlink($file)) {
                $cleaned++;
            }
        }
    }
}

echo "<p style='color: green;'>✅ Cleaned up $cleaned cache files</p>\n";

// Create a simple cache bypass helper
$cache_bypass_helper = __DIR__ . '/inc/core/dbmvs/cache_bypass_helper.php';
$helper_content = '<?php
/**
 * Cache Bypass Helper for Development
 */

// Check if cache should be disabled
function dooplay_is_cache_disabled() {
    $disable_file = __DIR__ . "/.cache_disabled";
    return file_exists($disable_file) || defined("DOOPLAY_CACHE_DISABLED");
}

// Safe cache write function
function dooplay_safe_cache_write($file, $data) {
    if (dooplay_is_cache_disabled()) {
        return false; // Skip caching
    }
    
    try {
        return file_put_contents($file, $data, LOCK_EX);
    } catch (Exception $e) {
        error_log("Dooplay Cache Error: " . $e->getMessage());
        return false;
    }
}

// Safe cache read function
function dooplay_safe_cache_read($file) {
    if (dooplay_is_cache_disabled()) {
        return false; // Skip caching
    }
    
    if (!file_exists($file)) {
        return false;
    }
    
    try {
        return file_get_contents($file);
    } catch (Exception $e) {
        error_log("Dooplay Cache Error: " . $e->getMessage());
        return false;
    }
}
';

if (file_put_contents($cache_bypass_helper, $helper_content)) {
    echo "<p style='color: green;'>✅ Cache bypass helper created</p>\n";
}

// Instructions for manual integration
echo "<h3>📋 Manual Integration Instructions</h3>\n";
echo "<p>To complete the cache bypass, you need to modify the helpers.php file:</p>\n";
echo "<ol>\n";
echo "<li>Open <code>inc/core/dbmvs/classes/helpers.php</code></li>\n";
echo "<li>Find the <code>GetAllSeasons</code> and <code>GetAllEpisodes</code> functions</li>\n";
echo "<li>Replace <code>file_put_contents(\$cfile, serialize(\$query));</code> with <code>dooplay_safe_cache_write(\$cfile, serialize(\$query));</code></li>\n";
echo "<li>Replace <code>file_get_contents(\$cfile)</code> with <code>dooplay_safe_cache_read(\$cfile)</code></li>\n";
echo "<li>Add <code>require_once __DIR__ . '/../cache_bypass_helper.php';</code> at the top of helpers.php</li>\n";
echo "</ol>\n";

// Alternative solution
echo "<h3>🔄 Alternative Solution</h3>\n";
echo "<p>If you continue to experience cache issues, you can:</p>\n";
echo "<ul>\n";
echo "<li><strong>Use WordPress Transients:</strong> Replace file caching with WordPress transient API</li>\n";
echo "<li><strong>Disable Caching Completely:</strong> Comment out all caching code for development</li>\n";
echo "<li><strong>Use Different Cache Directory:</strong> Move cache to wp-content/uploads/dooplay-cache/</li>\n";
echo "<li><strong>Run XAMPP as Administrator:</strong> This often resolves permission issues</li>\n";
echo "</ul>\n";

// WordPress transient example
echo "<h3>💡 WordPress Transient Example</h3>\n";
echo "<p>Here's how to replace file caching with WordPress transients:</p>\n";
echo "<pre style='background: #f0f0f0; padding: 10px; border-radius: 5px;'>\n";
echo "// Instead of file caching:\n";
echo "// \$data = file_get_contents(\$cache_file);\n\n";
echo "// Use WordPress transients:\n";
echo "\$cache_key = 'dooplay_seasons_' . \$tmdb;\n";
echo "\$data = get_transient(\$cache_key);\n";
echo "if (\$data === false) {\n";
echo "    // Generate data\n";
echo "    \$data = \$your_data;\n";
echo "    set_transient(\$cache_key, \$data, 12 * HOUR_IN_SECONDS);\n";
echo "}\n";
echo "</pre>\n";

// Status check
echo "<h3>✅ Status Check</h3>\n";
echo "<ul>\n";
echo "<li>Cache Directory: " . (is_dir($cache_dir) ? "✅ Exists" : "❌ Missing") . "</li>\n";
echo "<li>Cache Disabled Flag: " . (file_exists($cache_disable_file) ? "✅ Created" : "❌ Missing") . "</li>\n";
echo "<li>Cache Bypass Helper: " . (file_exists($cache_bypass_helper) ? "✅ Created" : "❌ Missing") . "</li>\n";
echo "<li>Directory Writable: " . (is_writable($cache_dir) ? "✅ Yes" : "❌ No") . "</li>\n";
echo "</ul>\n";

echo "<hr>\n";
echo "<p style='color: green;'><strong>✅ Cache disable script completed!</strong></p>\n";
echo "<p>Your Dooplay theme should now work without cache permission errors.</p>\n";
echo "<p><strong>Note:</strong> This is a temporary solution for development. For production, consider fixing the actual permission issues.</p>\n";

?>

<style>
body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
    max-width: 800px;
    margin: 20px auto;
    padding: 20px;
    background: #f1f1f1;
}

h2, h3 {
    color: #333;
    border-bottom: 2px solid #0073aa;
    padding-bottom: 10px;
}

p, li {
    line-height: 1.6;
}

code {
    background: #f0f0f0;
    padding: 2px 6px;
    border-radius: 3px;
    font-family: monospace;
}

pre {
    background: #f0f0f0;
    padding: 15px;
    border-radius: 5px;
    overflow-x: auto;
    font-family: monospace;
    font-size: 14px;
}

ul, ol {
    margin-left: 20px;
}

hr {
    border: none;
    border-top: 1px solid #ccc;
    margin: 30px 0;
}
</style>
