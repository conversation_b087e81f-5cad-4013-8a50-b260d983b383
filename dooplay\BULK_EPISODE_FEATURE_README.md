# Bulk Episode Links Feature - Deshiflix Admin

## 🎬 নতুন ফিচার: বাল্ক এপিসোড লিংক ম্যানেজমেন্ট

এই ফিচারটি আপনার Dooplay থিমে যুক্ত করা হয়েছে যাতে আপনি সহজেই সিরিজের একাধিক এপিসোডের ডাউনলোড লিংক একসাথে যুক্ত করতে পারেন।

## 🚀 মূল বৈশিষ্ট্য

### ✅ যা যা করতে পারবেন:
- **একসাথে ১০০টি পর্যন্ত এপিসোড** যুক্ত করুন
- **অটোমেটিক এপিসোড নাম্বারিং** (S01E01, S01E02, ইত্যাদি)
- **কমন সেটিংস** সব এপিসোডের জন্য (কোয়ালিটি, ভাষা, টাইপ)
- **আলাদা আলাদা লিংক এবং সাইজ** প্রতিটি এপিসোডের জন্য
- **কাস্টম সিজন নাম্বার** এবং শুরুর এপিসোড নাম্বার
- **রিয়েল-টাইম ভ্যালিডেশন** এবং এরর হ্যান্ডলিং

## 📍 কোথায় পাবেন

### WordPress Admin Panel এ:
1. **Dashboard** → **TV Shows** → **Add New** অথবা কোনো existing TV show edit করুন
2. নিচের দিকে **"Bulk Episode Links"** মেটাবক্স দেখতে পাবেন
3. এটি শুধুমাত্র **TV Shows** পোস্ট টাইপে দেখা যাবে

## 🛠️ কিভাবে ব্যবহার করবেন

### ধাপ ১: মূল সেটিংস
```
Number of Episodes: কতটি এপিসোড যুক্ত করতে চান (১-১০০)
Season Number: সিজন নাম্বার (যেমন: ১, ২, ৩)
Starting Episode Number: শুরুর এপিসোড নাম্বার (যেমন: ১)
```

### ধাপ ২: কমন সেটিংস
```
Link Type: Download / Watch online / Torrent
Quality: 720p / 1080p / 480p / 4K
Language: Bengali / Hindi / English / Tamil
```

### ধাপ ৩: ফিল্ড জেনারেট করুন
- **"Generate Fields"** বাটনে ক্লিক করুন
- আপনার নির্ধারিত সংখ্যক এপিসোডের ফিল্ড তৈরি হবে

### ধাপ ৪: লিংক যুক্ত করুন
প্রতিটি এপিসোডের জন্য:
```
Download URL: সম্পূর্ণ ডাউনলোড লিংক
File Size: ফাইলের সাইজ (যেমন: 450MB, 1.2GB)
```

### ধাপ ৫: সেভ করুন
- **"Save All Episode Links"** বাটনে ক্লিক করুন
- সিস্টেম অটোমেটিক এপিসোড পোস্ট এবং লিংক তৈরি করবে

## 📁 যুক্ত করা ফাইলসমূহ

### নতুন ফাইল:
```
dooplay/inc/core/dbmvs/classes/bulk_episode_handler.php
dooplay/inc/core/dbmvs/assets/bulk-episode-admin.css
```

### পরিবর্তিত ফাইল:
```
dooplay/inc/core/dbmvs/classes/metaboxes.php
dooplay/inc/core/dbmvs/classes/enqueues.php
dooplay/inc/core/dbmvs/init.php
```

## 🎯 উদাহরণ ব্যবহার

### Money Heist সিরিজের জন্য:
```
Number of Episodes: 5
Season Number: 1
Starting Episode Number: 1
Link Type: Download
Quality: 720p
Language: Bengali

Episode 1: https://example.com/money-heist-s01e01.mkv (450MB)
Episode 2: https://example.com/money-heist-s01e02.mkv (420MB)
Episode 3: https://example.com/money-heist-s01e03.mkv (380MB)
Episode 4: https://example.com/money-heist-s01e04.mkv (465MB)
Episode 5: https://example.com/money-heist-s01e05.mkv (440MB)
```

### ফলাফল:
- ৫টি এপিসোড পোস্ট তৈরি হবে
- প্রতিটিতে সঠিক মেটাডেটা থাকবে
- ডাউনলোড লিংক সহ সব তথ্য সেট হবে

## 🔧 টেকনিক্যাল বিবরণ

### AJAX Functionality:
- `save_bulk_episode_links` action hook
- Real-time form validation
- Progress tracking
- Error handling

### Database Structure:
```sql
Episodes Table:
- ids (TMDb ID)
- temporada (Season Number)
- episodio (Episode Number)
- serie (Series Name)
- episode_name (Episode Title)

Links Table:
- _dool_url (Download URL)
- _dool_type (Link Type)
- _dool_quality (Quality)
- _dool_lang (Language)
- _dool_size (File Size)
```

## 🛡️ নিরাপত্তা

- **Nonce Verification** সব AJAX রিকোয়েস্টে
- **User Permission Check** (edit_posts capability)
- **Input Validation** এবং Sanitization
- **SQL Injection Protection**

## 🎨 UI/UX Features

- **Responsive Design** মোবাইল এবং ডেস্কটপে
- **Real-time Validation** ইনপুট ফিল্ডে
- **Progress Indicators** সেভ করার সময়
- **Success/Error Messages** ব্যবহারকারীর জন্য
- **Clean Interface** WordPress admin স্টাইলে

## 🔄 ভবিষ্যতের আপডেট

### পরিকল্পিত ফিচার:
- **Bulk Edit** existing episodes
- **Import from CSV** file
- **Template System** for common series
- **Auto-numbering** from existing episodes
- **Duplicate Detection** and prevention

## 📞 সাপোর্ট

এই ফিচার সম্পর্কে কোনো প্রশ্ন বা সমস্যা থাকলে:
- কোড রিভিউ করুন
- ডেমো ফাইল দেখুন (`bulk-episode-demo.html`)
- WordPress debug log চেক করুন

## 🎉 সুবিধা

### আগে:
- প্রতিটি এপিসোড আলাদা আলাদা তৈরি করতে হতো
- লিংক যুক্ত করতে অনেক সময় লাগতো
- এপিসোড নাম্বারিং ম্যানুয়াল করতে হতো

### এখন:
- একসাথে সব এপিসোড তৈরি করুন
- অটোমেটিক নাম্বারিং এবং মেটাডেটা
- দ্রুত এবং দক্ষ ওয়ার্কফ্লো

---

**🎬 Happy Streaming with Deshiflix! 🎬**
