<?php
/*
* ----------------------------------------------------
* @author: Doothemes
* <AUTHOR> https://doothemes.com/
* @copyright: (c) 2021 Doothemes. All rights reserved
* ----------------------------------------------------
* @since 2.5.0
*/

class DDbmoviesBulkEpisodeHandler {

    /**
     * @since 2.5.0
     * @version 1.0
     */
    public function __construct(){
        add_action('wp_ajax_save_bulk_episode_links', array($this, 'save_bulk_episode_links'));
        add_action('wp_ajax_nopriv_save_bulk_episode_links', array($this, 'save_bulk_episode_links'));
    }

    /**
     * Save bulk episode links via AJAX
     * @since 2.5.0
     * @version 1.0
     */
    public function save_bulk_episode_links(){
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], '_bulk_episode_links_nonce')) {
            wp_send_json_error(__d('Security check failed'));
            return;
        }

        // Check user permissions
        if (!current_user_can('edit_posts')) {
            wp_send_json_error(__d('Insufficient permissions'));
            return;
        }

        $post_id = intval($_POST['post_id']);
        $episodes = $_POST['episodes'];

        if (!$post_id || !is_array($episodes)) {
            wp_send_json_error(__d('Invalid data provided'));
            return;
        }

        $created_episodes = array();
        $errors = array();

        foreach ($episodes as $episode_data) {
            try {
                $episode_id = $this->create_episode_with_links($post_id, $episode_data);
                if ($episode_id) {
                    $created_episodes[] = $episode_id;
                } else {
                    $errors[] = sprintf(__d('Failed to create episode %s'), $episode_data['episode']);
                }
            } catch (Exception $e) {
                $errors[] = sprintf(__d('Error creating episode %s: %s'), $episode_data['episode'], $e->getMessage());
            }
        }

        if (!empty($created_episodes)) {
            $message = sprintf(__d('Successfully created %d episodes'), count($created_episodes));
            if (!empty($errors)) {
                $message .= '. ' . sprintf(__d('Errors: %s'), implode(', ', $errors));
            }
            wp_send_json_success($message);
        } else {
            wp_send_json_error(__d('No episodes were created. ') . implode(', ', $errors));
        }
    }

    /**
     * Create episode post with download links
     * @since 2.5.0
     * @version 1.0
     */
    private function create_episode_with_links($series_id, $episode_data) {
        // Get series information
        $series_title = get_the_title($series_id);
        $series_tmdb_id = get_post_meta($series_id, 'ids', true);

        // Create episode title
        $episode_title = sprintf('%s: S%02dE%02d', 
            $series_title, 
            intval($episode_data['season']), 
            intval($episode_data['episode'])
        );

        // Check if episode already exists
        $existing_episode = get_posts(array(
            'post_type' => 'episodes',
            'meta_query' => array(
                array(
                    'key' => 'ids',
                    'value' => $series_tmdb_id
                ),
                array(
                    'key' => 'temporada',
                    'value' => $episode_data['season']
                ),
                array(
                    'key' => 'episodio',
                    'value' => $episode_data['episode']
                )
            ),
            'posts_per_page' => 1
        ));

        if (!empty($existing_episode)) {
            $episode_id = $existing_episode[0]->ID;
        } else {
            // Create new episode post
            $episode_post = array(
                'post_title' => $episode_title,
                'post_type' => 'episodes',
                'post_status' => 'publish',
                'post_author' => get_current_user_id(),
                'post_parent' => 0
            );

            $episode_id = wp_insert_post($episode_post);

            if (is_wp_error($episode_id)) {
                return false;
            }

            // Set episode metadata
            update_post_meta($episode_id, 'ids', $series_tmdb_id);
            update_post_meta($episode_id, 'temporada', $episode_data['season']);
            update_post_meta($episode_id, 'episodio', $episode_data['episode']);
            update_post_meta($episode_id, 'serie', $series_title);
            update_post_meta($episode_id, 'episode_name', sprintf('Episode %d', $episode_data['episode']));
        }

        // Create download link
        $link_title = sprintf('S%02dE%02d %s %s', 
            intval($episode_data['season']), 
            intval($episode_data['episode']),
            $episode_data['quality'],
            $episode_data['language']
        );

        $link_post = array(
            'post_title' => $link_title,
            'post_type' => 'dt_links',
            'post_status' => 'publish',
            'post_author' => get_current_user_id(),
            'post_parent' => $episode_id
        );

        $link_id = wp_insert_post($link_post);

        if (is_wp_error($link_id)) {
            return false;
        }

        // Set link metadata
        update_post_meta($link_id, '_dool_url', $episode_data['url']);
        update_post_meta($link_id, '_dool_type', $episode_data['type']);
        update_post_meta($link_id, '_dool_quality', $episode_data['quality']);
        update_post_meta($link_id, '_dool_lang', $episode_data['language']);
        
        if (!empty($episode_data['size'])) {
            update_post_meta($link_id, '_dool_size', $episode_data['size']);
        }

        return $episode_id;
    }

    /**
     * Create season if it doesn't exist
     * @since 2.5.0
     * @version 1.0
     */
    private function create_season_if_not_exists($series_id, $season_number) {
        $series_tmdb_id = get_post_meta($series_id, 'ids', true);
        $series_title = get_the_title($series_id);

        // Check if season exists
        $existing_season = get_posts(array(
            'post_type' => 'seasons',
            'meta_query' => array(
                array(
                    'key' => 'ids',
                    'value' => $series_tmdb_id
                ),
                array(
                    'key' => 'temporada',
                    'value' => $season_number
                )
            ),
            'posts_per_page' => 1
        ));

        if (!empty($existing_season)) {
            return $existing_season[0]->ID;
        }

        // Create new season
        $season_title = sprintf('%s: Season %d', $series_title, $season_number);
        
        $season_post = array(
            'post_title' => $season_title,
            'post_type' => 'seasons',
            'post_status' => 'publish',
            'post_author' => get_current_user_id(),
            'post_parent' => 0
        );

        $season_id = wp_insert_post($season_post);

        if (!is_wp_error($season_id)) {
            update_post_meta($season_id, 'ids', $series_tmdb_id);
            update_post_meta($season_id, 'temporada', $season_number);
            update_post_meta($season_id, 'serie', $series_title);
        }

        return $season_id;
    }
}

new DDbmoviesBulkEpisodeHandler;
