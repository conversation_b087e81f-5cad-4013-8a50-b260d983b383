<?php
/*
* ----------------------------------------------------
* @author: Doothemes
* <AUTHOR> https://doothemes.com/
* @copyright: (c) 2021 Doothemes. All rights reserved
* ----------------------------------------------------
* @since 2.5.0
*/

class DDbmoviesMetaboxes extends DDbmoviesHelpers{

    /**
     * @since 2.5.0
     * @version 1.0
     */
    public function __construct(){
        add_action('add_meta_boxes',array(&$this,'metaboxes'));
    }

    /**
     * @since 2.5.0
     * @version 1.0
     */
    public function metaboxes(){
        add_meta_box('dt_metabox',__d('Movie Info'),array(&$this,'meta_movies'),'movies','normal','high');
        add_meta_box('dt_metabox',__d('TVShow Info'),array(&$this,'meta_tvshows'),'tvshows','normal','high');
        add_meta_box('dt_metabox',__d('Season Info'),array(&$this,'meta_seasons'),'seasons','normal','high');
        add_meta_box('dt_metabox',__d('Episode Info'),array(&$this,'meta_episodes'),'episodes','normal','high');

        // Add bulk episode links metabox for series
        add_meta_box('dt_bulk_episode_links',__d('Bulk Episode Links'),array(&$this,'meta_bulk_episode_links'),'tvshows','normal','high');
    }

    /**
     * @since 2.5.0
     * @version 1.0
     */
    public function meta_movies(){
        // Nonce security
	    wp_nonce_field('_movie_nonce', 'movie_nonce');
		// Metabox options
		$options = array(
	        array(
	            'id'            => 'ids',
				'id2'		    => null,
				'id3'		    => null,
	            'type'          => 'generator',
	            'style'         => 'style="background: #f7f7f7"',
	            'class'         => 'regular-text',
	            'placeholder'   => 'tt2911666',
	            'label'         => __d('Generate data'),
	            'desc'          => __d('Generate data from <strong>imdb.com</strong>'),
	            'fdesc'         => __d('E.g. http://www.imdb.com/title/<strong>tt2911666</strong>/'),
                'requireupdate' => true,
                'previewpost'   => false
	        ),
	        array(
	            'id'     => 'dt_featured_post',
	            'type'   => 'checkbox',
	            'label'  => __d('Featured Title'),
	            'clabel' => __d('Do you want to mark this title as a featured item?')
	        ),
	        array(
	            'type'    => 'heading',
	            'colspan' => 2,
	            'text'    => __d('Images and trailer')
	        ),
	        array(
	            'id'    => 'dt_poster',
	            'type'  => 'text',
	            'label' => __d('Poster'),
	            'desc'  => __d('Add url image')
	        ),
	        array(
	            'id'      => 'dt_backdrop',
	            'type'    => 'text',
	            'label'   => __d('Main Backdrop'),
	            'desc'    => __d('Add url image')
	        ),
	        array(
	            'id'     => 'imagenes',
	            'type'   => 'textarea',
	            'rows'   => 5,
	            'aid'    => 'up_images_images',
	            'label'  => __d('Backdrops'),
	            'desc'   => __d('Place each image url below another')
	        ),
	        array(
	            'id'    => 'youtube_id',
	            'type'  => 'text',
	            'class' => 'small-text',
	            'label' => __d('Video trailer'),
	            'desc'  => __d('Add id Youtube video'),
	            'fdesc' => '[id_video_youtube]',
				'double' => null,
	        ),
	        array(
	            'type'    => 'heading',
	            'colspan' => 2,
	            'text'    => __d('IMDb.com data')
	        ),
	        array(
	            'double' => true,
	            'id'     => 'imdbRating',
	            'id2'    => 'imdbVotes',
	            'type'   => 'text',
	            'label'  => __d('Rating IMDb'),
	            'desc'   => __d('Average / votes')
	        ),
	        array(
	            'id'    => 'Rated',
	            'type'  => 'text',
	            'class' => 'small-text',
				'double' => null,
				'fdesc'	=> null,
	            'label' => __d('Rated')
	        ),
	        array(
	            'id'    => 'Country',
	            'type'  => 'text',
	            'class' => 'small-text',
				'fdesc'	=> null,
				'desc'	=> null,
				'double' => null,
	            'label' => __d('Country')
	        ),
	        array(
	            'type'    => 'heading',
	            'colspan' => 2,
	            'text' => __d('Themoviedb.org data')
	        ),
	        array(
	            'id'    => 'idtmdb',
	            'type'  => 'text',
	            'class' => 'small-text',
				'fdesc'	=> null,
				'desc'	=> null,
				'double' => null,
				'class' => null,
	            'label' => __d('ID TMDb')
	        ),
	        array(
	            'id'    => 'original_title',
	            'type'  => 'text',
	            'class' => 'small-text',
				'fdesc'	=> null,
				'double' => null,
				'class' => null,
				'desc' => null,
	            'label' => __d('Original title')
	        ),
	        array(
	            'id'    => 'tagline',
	            'type'  => 'text',
	            'class' => 'small-text',
				'fdesc'	=> null,
				'double' => null,
				'desc' => null,
	            'label' => __d('Tag line')
	        ),
	        array(
	            'id'    => 'release_date',
	            'type'  => 'date',
	            'label' => __d('Release Date')
	        ),
	        array(
	            'double' => true,
	            'id'     => 'vote_average',
	            'id2'    => 'vote_count',
	            'type'   => 'text',
	            'label'  => __d('Rating TMDb'),
	            'desc'   => __d('Average / votes')
	        ),
	        array(
	            'id'    => 'runtime',
	            'type'  => 'text',
	            'class' => 'small-text',
	            'label' => __d('Runtime')
	        ),
	        array(
	            'id' => 'dt_cast',
	            'type' => 'textarea',
	            'rows' => 5,
				'upload' => false,
	            'label' => __d('Cast')
	        ),
	        array(
	            'id'    => 'dt_dir',
	            'type'  => 'text',
	            'label' => __d('Director')
	        )
	    );
	    $this->ViewMeta($options);
    }

    /**
     * @since 2.5.0
     * @version 1.0
     */
    public function meta_tvshows(){
        // Nonce security
	    wp_nonce_field('_tvshows_nonce', 'tvshows_nonce');
		// Metabox options
	    $options = array(
	        array(
	            'id'            => 'ids',
	            'type'          => 'generator',
	            'style'         => 'style="background: #f7f7f7"',
	            'class'         => 'regular-text',
	            'placeholder'   => '1402',
	            'label'         => __d('Generate data'),
	            'desc'          => __d('Generate data from <strong>themoviedb.org</strong>'),
	            'fdesc'         => __d('E.g. https://www.themoviedb.org/tv/<strong>1402</strong>-the-walking-dead'),
                'requireupdate' => true,
                'previewpost'   => false
	        ),
	        array(
	            'id'     => 'clgnrt',
	            'type'   => 'checkbox',
	            'label'  => __d('Seasons control'),
	            'clabel' => __d('I have generated seasons or I will manually')
	        ),
	        array(
	            'id'     => 'dt_featured_post',
	            'type'   => 'checkbox',
	            'label'  => __d('Featured Title'),
	            'clabel' => __d('Do you want to mark this title as a featured item?')
	        ),
	        array(
	            'type'    => 'heading',
	            'colspan' => 2,
	            'text'    => __d('Images and trailer')
	        ),
	        array(
	            'id'    => 'dt_poster',
	            'type'  => 'text',
	            'label' => __d('Poster'),
	            'desc'  => __d('Add url image')
	        ),
	        array(
	            'id'      => 'dt_backdrop',
	            'type'    => 'text',
	            'label'   => __d('Main Backdrop'),
	            'desc'    => __d('Add url image')
	        ),
	        array(
	            'id'     => 'imagenes',
	            'type'   => 'textarea',
	            'rows'   => 5,
	            'label'  => __d('Backdrops'),
	            'desc'   => __d('Place each image url below another')
	        ),
	        array(
	            'id'    => 'youtube_id',
	            'type'  => 'text',
	            'class' => 'small-text',
	            'label' => __d('Video trailer'),
	            'desc'  => __d('Add id Youtube video'),
	            'fdesc' => '[id_video_youtube]'
	        ),
	        array(
	            'type'    => 'heading',
	            'colspan' => 2,
	            'text'    => __d('More data')
	        ),
	        array(
	            'id'    => 'original_name',
	            'type'  => 'text',
	            'class' => 'small-text',
	            'label' => __d('Original Name')
	        ),
	        array(
	            'id'    => 'first_air_date',
	            'type'  => 'date',
	            'label' => __d('Firt air date')
	        ),
	        array(
	            'id'    => 'last_air_date',
	            'type'  => 'date',
	            'label' => __d('Last air date')
	        ),
	        array(
	            'double' => true,
	            'id'     => 'number_of_seasons',
	            'id2'    => 'number_of_episodes',
	            'type'   => 'text',
	            'label'  => __d('Content total posted'),
	            'desc'   => __d('Seasons / Episodes')
	        ),
	        array(
	            'double' => true,
	            'id'     => 'imdbRating',
	            'id2'    => 'imdbVotes',
	            'type'   => 'text',
	            'label'  => __d('Rating TMDb'),
	            'desc'   => __d('Average / votes')
	        ),
	        array(
	            'id'    => 'episode_run_time',
	            'type'  => 'text',
	            'class' => 'small-text',
	            'label' => __d('Episode runtime')
	        ),
	        array(
	            'id' => 'dt_cast',
	            'type' => 'textarea',
	            'rows' => 5,
	            'label' => __d('Cast')
	        ),
	        array(
	            'id'    => 'dt_creator',
	            'type'  => 'text',
	            'label' => __d('Creator')
	        )
	    );
	    $this->ViewMeta($options);
    }

    /**
     * @since 2.5.0
     * @version 1.0
     */
    public function meta_seasons(){
        // Nonce security
	    wp_nonce_field('_seasons_nonce', 'seasons_nonce');
	    // Metabox options
	    $options = array(
	        array(
	            'id'           => 'ids',
	            'id2'          => 'temporada',
	            'type'         => 'generator',
	            'style'        => 'style="background: #f7f7f7"',
	            'class'        => 'extra-small-text',
	            'placeholder'  => '1402',
	            'placeholder2' => '1',
	            'label'        => __d('Generate data'),
	            'desc'         => __d('Generate data from <strong>themoviedb.org</strong>'),
	            'fdesc'        => __d('E.g. https://www.themoviedb.org/tv/<strong>1402</strong>-the-walking-dead/season/<strong>1</strong>/'),
                'requireupdate' => true,
                'previewpost'   => $this->get_option('nospostimp')
	        ),
	        array(
	            'id'     => 'clgnrt',
	            'type'   => 'checkbox',
	            'label'  => __d('Episodes control'),
	            'clabel' => __d('I generated episodes or add manually')
	        ),
	        array(
	            'id'    => 'serie',
	            'type'  => 'text',
	            'label' => __d('Serie name')
	        ),
	        array(
	            'id'    => 'dt_poster',
	            'type'  => 'text',
	            'label' => __d('Poster'),
	            'desc'  => __d('Add url image')
	        ),
	        array(
	            'id'    => 'air_date',
	            'type'  => 'date',
	            'label' => __d('Air date')
	        )
	    );
	    $this->ViewMeta($options);
    }

    /**
     * @since 2.5.0
     * @version 1.0
     */
    public function meta_episodes(){
        // Nonce security
	    wp_nonce_field('_episodios_nonce','episodios_nonce');
	    // Metabox options
	    $options = array(
	        array(
	            'id'           => 'ids',
	            'id2'          => 'temporada',
	            'id3'          => 'episodio',
	            'type'         => 'generator',
	            'style'        => 'style="background: #f7f7f7"',
	            'class'        => 'extra-small-text',
	            'placeholder'  => '1402',
	            'placeholder2' => '1',
	            'placeholder3' => '2',
	            'label'        => __d('Generate data'),
	            'desc'         => __d('Generate data from <strong>themoviedb.org</strong>'),
	            'fdesc'        => __d('E.g. https://www.themoviedb.org/tv/<strong>1402</strong>-the-walking-dead/season/<strong>1</strong>/episode/<strong>2</strong>'),
                'requireupdate' => true,
                'previewpost'   => $this->get_option('nospostimp')
	        ),
	        array(
	            'id'    => 'episode_name',
	            'type'  => 'text',
	            'label' => __d('Episode title')
	        ),
	        array(
	            'id'    => 'serie',
	            'type'  => 'text',
	            'label' => __d('Serie name')
	        ),
	        array(
	            'id'      => 'dt_backdrop',
	            'type'    => 'text',
	            'label'   => __d('Main Backdrop'),
	            'desc'    => __d('Add url image')
	        ),
	        array(
	            'id'     => 'imagenes',
	            'type'   => 'textarea',
	            'rows'   => 5,
	            'label'  => __d('Backdrops'),
	            'desc'   => __d('Place each image url below another')
	        ),
	        array(
	            'id'    => 'air_date',
	            'type'  => 'date',
	            'label' => __d('Air date')
	        )
	    );
	    $this->ViewMeta($options);
    }

    /**
     * @since 2.5.0
     * @version 1.0
     */
    public function meta_bulk_episode_links(){
        global $post;
        // Nonce security
        wp_nonce_field('_bulk_episode_links_nonce', 'bulk_episode_links_nonce');

        echo '<div id="bulk-episode-links-container">';
        echo '<table class="options-table-responsive dt-options-table">';
        echo '<tbody>';

        // Number of episodes field
        echo '<tr>';
        echo '<td class="label"><label>' . __d('Number of Episodes') . '</label></td>';
        echo '<td class="field">';
        echo '<input type="number" id="episode_count" name="episode_count" min="1" max="100" value="1" style="width: 80px;">';
        echo '<button type="button" id="generate_episode_fields" class="button button-secondary" style="margin-left: 10px;">' . __d('Generate Fields') . '</button>';
        echo '</td>';
        echo '</tr>';

        // Season number field
        echo '<tr>';
        echo '<td class="label"><label>' . __d('Season Number') . '</label></td>';
        echo '<td class="field">';
        echo '<input type="number" id="season_number" name="season_number" min="1" value="1" style="width: 80px;">';
        echo '</td>';
        echo '</tr>';

        // Starting episode number
        echo '<tr>';
        echo '<td class="label"><label>' . __d('Starting Episode Number') . '</label></td>';
        echo '<td class="field">';
        echo '<input type="number" id="starting_episode" name="starting_episode" min="1" value="1" style="width: 80px;">';
        echo '</td>';
        echo '</tr>';

        // Common fields for all episodes
        echo '<tr>';
        echo '<td class="label"><label>' . __d('Link Type') . '</label></td>';
        echo '<td class="field">';
        echo '<select id="common_link_type" name="common_link_type">';
        echo '<option value="' . __d('Download') . '">' . __d('Download') . '</option>';
        echo '<option value="' . __d('Watch online') . '">' . __d('Watch online') . '</option>';
        echo '<option value="' . __d('Torrent') . '">' . __d('Torrent') . '</option>';
        echo '</select>';
        echo '</td>';
        echo '</tr>';

        echo '<tr>';
        echo '<td class="label"><label>' . __d('Quality') . '</label></td>';
        echo '<td class="field">';
        echo '<select id="common_quality" name="common_quality">';
        echo '<option value="720p">720p</option>';
        echo '<option value="1080p">1080p</option>';
        echo '<option value="480p">480p</option>';
        echo '<option value="4K">4K</option>';
        echo '</select>';
        echo '</td>';
        echo '</tr>';

        echo '<tr>';
        echo '<td class="label"><label>' . __d('Language') . '</label></td>';
        echo '<td class="field">';
        echo '<select id="common_language" name="common_language">';
        echo '<option value="Bengali">Bengali</option>';
        echo '<option value="Hindi">Hindi</option>';
        echo '<option value="English">English</option>';
        echo '<option value="Tamil">Tamil</option>';
        echo '</select>';
        echo '</td>';
        echo '</tr>';

        // Dynamic episode fields container
        echo '<tr>';
        echo '<td colspan="2">';
        echo '<div id="episode_fields_container" style="margin-top: 20px;"></div>';
        echo '</td>';
        echo '</tr>';

        // Save button
        echo '<tr>';
        echo '<td colspan="2">';
        echo '<button type="button" id="save_bulk_episodes" class="button button-primary" style="margin-top: 15px;">' . __d('Save All Episode Links') . '</button>';
        echo '<div id="bulk_save_status" style="margin-top: 10px;"></div>';
        echo '</td>';
        echo '</tr>';

        echo '</tbody>';
        echo '</table>';
        echo '</div>';

        // Add JavaScript for dynamic functionality
        $this->add_bulk_episode_scripts();
    }

    /**
     * Add JavaScript for bulk episode functionality
     */
    private function add_bulk_episode_scripts(){
        ?>
        <script type="text/javascript">
        jQuery(document).ready(function($) {
            // Generate episode fields
            $('#generate_episode_fields').click(function() {
                var episodeCount = parseInt($('#episode_count').val());
                var seasonNumber = parseInt($('#season_number').val());
                var startingEpisode = parseInt($('#starting_episode').val());
                var container = $('#episode_fields_container');

                container.empty();

                if (episodeCount > 0 && episodeCount <= 100) {
                    container.append('<h3><?php echo __d("Episode Links"); ?></h3>');

                    for (var i = 0; i < episodeCount; i++) {
                        var episodeNum = startingEpisode + i;
                        var fieldHtml = '<div class="episode-field" style="border: 1px solid #ddd; padding: 15px; margin-bottom: 10px; background: #f9f9f9;">';
                        fieldHtml += '<h4><?php echo __d("Episode"); ?> ' + episodeNum + '</h4>';
                        fieldHtml += '<table style="width: 100%;">';
                        fieldHtml += '<tr>';
                        fieldHtml += '<td style="width: 150px;"><label><?php echo __d("Download URL"); ?>:</label></td>';
                        fieldHtml += '<td><input type="url" name="episode_urls[]" class="regular-text" placeholder="<?php echo __d("Enter download link"); ?>" required></td>';
                        fieldHtml += '</tr>';
                        fieldHtml += '<tr>';
                        fieldHtml += '<td><label><?php echo __d("File Size"); ?>:</label></td>';
                        fieldHtml += '<td><input type="text" name="episode_sizes[]" class="regular-text" placeholder="<?php echo __d("e.g., 350MB, 1.2GB"); ?>"></td>';
                        fieldHtml += '</tr>';
                        fieldHtml += '<input type="hidden" name="episode_numbers[]" value="' + episodeNum + '">';
                        fieldHtml += '<input type="hidden" name="season_numbers[]" value="' + seasonNumber + '">';
                        fieldHtml += '</table>';
                        fieldHtml += '</div>';

                        container.append(fieldHtml);
                    }

                    $('#save_bulk_episodes').show();
                } else {
                    alert('<?php echo __d("Please enter a valid number of episodes (1-100)"); ?>');
                }
            });

            // Save bulk episodes
            $('#save_bulk_episodes').click(function() {
                var button = $(this);
                var statusDiv = $('#bulk_save_status');

                button.prop('disabled', true).text('<?php echo __d("Saving..."); ?>');
                statusDiv.html('<div style="color: blue;"><?php echo __d("Processing episodes..."); ?></div>');

                // Collect all episode data
                var episodeData = [];
                $('.episode-field').each(function(index) {
                    var url = $(this).find('input[name="episode_urls[]"]').val();
                    var size = $(this).find('input[name="episode_sizes[]"]').val();
                    var episodeNum = $(this).find('input[name="episode_numbers[]"]').val();
                    var seasonNum = $(this).find('input[name="season_numbers[]"]').val();

                    if (url) {
                        episodeData.push({
                            url: url,
                            size: size,
                            episode: episodeNum,
                            season: seasonNum,
                            type: $('#common_link_type').val(),
                            quality: $('#common_quality').val(),
                            language: $('#common_language').val()
                        });
                    }
                });

                if (episodeData.length > 0) {
                    // Send AJAX request to save episodes
                    $.ajax({
                        url: ajaxurl,
                        type: 'POST',
                        data: {
                            action: 'save_bulk_episode_links',
                            post_id: <?php echo $GLOBALS['post']->ID; ?>,
                            episodes: episodeData,
                            nonce: $('#bulk_episode_links_nonce').val()
                        },
                        success: function(response) {
                            if (response.success) {
                                statusDiv.html('<div style="color: green;"><?php echo __d("Episodes saved successfully!"); ?></div>');
                                // Clear the form
                                $('#episode_fields_container').empty();
                                $('#episode_count').val(1);
                            } else {
                                statusDiv.html('<div style="color: red;"><?php echo __d("Error saving episodes: "); ?>' + response.data + '</div>');
                            }
                        },
                        error: function() {
                            statusDiv.html('<div style="color: red;"><?php echo __d("AJAX error occurred"); ?></div>');
                        },
                        complete: function() {
                            button.prop('disabled', false).text('<?php echo __d("Save All Episode Links"); ?>');
                        }
                    });
                } else {
                    statusDiv.html('<div style="color: red;"><?php echo __d("Please add at least one episode URL"); ?></div>');
                    button.prop('disabled', false).text('<?php echo __d("Save All Episode Links"); ?>');
                }
            });

            // Initially hide save button
            $('#save_bulk_episodes').hide();
        });
        </script>
        <?php
    }

    /**
     * @since 2.5.0
     * @version 1.0
     */
    private function ViewMeta($options){
        echo '<div id="loading_api"></div>';
	    echo '<div id="api_table"><table class="options-table-responsive dt-options-table"><tbody>';
		new Doofields($options);
	    echo '</tbody></table></div>';
    }
}

new DDbmoviesMetaboxes;
